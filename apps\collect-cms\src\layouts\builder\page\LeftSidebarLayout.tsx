import { Icon, useIsomorphicLayoutEffect } from '@collective/core'
import type { IComponentProps } from '@collective/integration-lib/cms'
import cn from 'classnames'
import { useContext, useState, useMemo } from 'react'
import { ComponentList, FieldEditor } from '@/components/Builder'
import { PageBuilderContext } from '@/contexts/BuilderContext'
import type { IServerComponentProps } from 'common'
import { LayerSidebarLayout } from './LayerSidebarLayout'
import styles from './pagebuilderlayout.module.scss'

export const LeftSidebarLayout = () => {
	const context = useContext(PageBuilderContext)
	const {
		expandedSidebar,
		editingIden,
		data,
		components,
		setData,
		setEditingIden,
		childComponentData,
		setChildComponentData,
		activeMediaId,
		mediaInfoData,
	} = context
	const [componentToEditId, setComponentToEditId] = useState<number>(-1)
	const [cmpData, setCmpData] = useState<IServerComponentProps | undefined>()

	// Filtered component's attributes
	const filteredComponents = (obj: IComponentProps) =>
		Object.entries(obj || {}).filter(([, value]) => typeof value === 'object' && value !== null)

	// Get sectionData directly from data on each render to always have the latest data
	const sectionData = useMemo(() => {
		return data?.data.components.find(
			(component) => component.id === editingIden.id || component.__temp_key__ === editingIden.id
		)
	}, [data, editingIden])

	useIsomorphicLayoutEffect(() => {
		setComponentToEditId(editingIden.id)
	}, [editingIden])

	useIsomorphicLayoutEffect(() => {
		setCmpData(components.data.find((comp) => comp.uid === sectionData?.__component))
	}, [sectionData, components.data])

	useIsomorphicLayoutEffect(() => {
		!expandedSidebar.left && setChildComponentData([])
	}, [expandedSidebar.left])

	return (
		<aside className={cn(styles.sidebar, !expandedSidebar.left && styles.is__hidden)}>
			{cmpData && sectionData && componentToEditId !== -1 ? (
				<div className={styles.component__wrapper}>
					{childComponentData &&
						childComponentData[0]?.name !== '' &&
						!activeMediaId &&
						(!mediaInfoData || mediaInfoData.name === '') &&
						<LayerSidebarLayout />}
					<div className={styles.component__title}>
						<button onClick={() => setEditingIden({ key: '', id: -1 })}>
							<Icon type="cms" variant="chevron-left" />
						</button>
						<h5 className="collect__heading collect__heading--h5">
							{cmpData?.schema.displayName || cmpData.uid}
						</h5>
					</div>
					<div className={styles.editor__components}>
						{filteredComponents(cmpData.schema.attributes).map(([key, value]) => {
							const val = value as {
								type: string
							}
							return (
								<FieldEditor
									key={key}
									{...val}
									name={key}
									size={12}
									layerPos="left"
									value={sectionData[key]}
									onChange={(props) => {
										setData((prevData) => {
											const newData = { ...prevData }
											const { field, value } = props
											const curCmpIndex = newData.data.components.findIndex(
												(data) => data.__component === editingIden.key && data.id === editingIden.id
											)
											if (curCmpIndex === -1) {
												return newData
											}

											newData.data.components[curCmpIndex] = {
												...newData.data.components[curCmpIndex],
												[field.trim()]: value,
											}
											// console.log(`[FieldEditor] New data after update:`, newData)
											return newData
										})
									}}
								/>
							)
						})}
					</div>
				</div>
			) : (
				<ComponentList />
			)}
		</aside>
	)
}
