"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/RightSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RightSidebarLayout: function() { return /* binding */ RightSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_to_property_key */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_property_key.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar RightSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, contentType = context.contentType, configuration = context.configuration, data = context.data, setData = context.setData, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, activeMediaId = context.activeMediaId, mediaInfoData = context.mediaInfoData;\n    var _ref = data || {}, commonData = _ref.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var globalFields = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return [];\n        var _configuration_data_contentType = configuration.data.contentType, layouts = _configuration_data_contentType.layouts, settings = _configuration_data_contentType.settings;\n        var mainFieldKey = settings.mainField;\n        var _contentType_data_schema_attributes = contentType.data.schema.attributes, mainField = _contentType_data_schema_attributes[mainFieldKey], components = _contentType_data_schema_attributes.components, fields = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__._)(_contentType_data_schema_attributes, [\n            mainFieldKey,\n            \"components\"\n        ].map(_swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__._));\n        var normalizedFields = layouts.edit.flat().filter(function(item) {\n            return ![\n                \"components\",\n                mainFieldKey\n            ].includes(item.name);\n        }).map(function(item) {\n            return (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, item, fields[item.name]);\n        }).filter(function(item) {\n            return \"type\" in item;\n        });\n        return normalizedFields;\n    }, [\n        contentType,\n        configuration\n    ]);\n    (0,_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.right && setChildComponentData([]);\n    }, [\n        expandedSidebar.right\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().sidebar), !expandedSidebar.right && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().is__hidden)),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"black\"),\n                onClick: function() {\n                    var newPath = pathname.replace(\"content-builder\", \"content-manager\").split(\"/\");\n                    if (newPath[newPath.length - 1] !== \"edit\") {\n                        newPath.push(\"edit\");\n                    }\n                    router.push(newPath.join(\"/\"));\n                },\n                children: \"Switch to Manager Mode\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 58,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                children: [\n                    childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__.LayerSidebarLayout, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 66\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__title),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: \"Page Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 5\n                    }, _this),\n                    globalFields === null || globalFields === void 0 ? void 0 : globalFields.map(function(field, idx) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_12__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, field), {\n                            layerPos: \"right\",\n                            value: commonData && commonData[field.name],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, prevData);\n                                    var _$field = props.field, value = props.value;\n                                    newData.data = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newData.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__._)({}, _$field.trim(), value));\n                                    console.log(\"[FieldEditor] New data after update:\", newData);\n                                    return newData;\n                                });\n                            }\n                        }), idx, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 6\n                        }, _this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 70,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, _this);\n};\n_s(RightSidebarLayout, \"YtHEQYDdwA8ymYZ75lBB3KCamkk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect\n    ];\n});\n_c = RightSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"RightSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx\n"));

/***/ })

});