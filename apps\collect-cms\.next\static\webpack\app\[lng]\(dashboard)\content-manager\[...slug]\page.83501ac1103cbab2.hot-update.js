"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx":
/*!********************************************************!*\
  !*** ./src/layouts/builder/page/LeftSidebarLayout.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeftSidebarLayout: function() { return /* binding */ LeftSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/ComponentList/ComponentList.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar LeftSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, editingIden = context.editingIden, data = context.data, components = context.components, setData = context.setData, setEditingIden = context.setEditingIden, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, activeMediaId = context.activeMediaId, mediaInfoData = context.mediaInfoData;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1), 2), componentToEditId = _useState[0], setComponentToEditId = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), 2), cmpData = _useState1[0], setCmpData = _useState1[1];\n    // Filtered component's attributes\n    var filteredComponents = function(obj) {\n        return Object.entries(obj || {}).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), value = _param[1];\n            return typeof value === \"object\" && value !== null;\n        });\n    };\n    // Get sectionData directly from data on each render to always have the latest data\n    var sectionData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        return data === null || data === void 0 ? void 0 : data.data.components.find(function(component) {\n            return component.id === editingIden.id || component.__temp_key__ === editingIden.id;\n        });\n    }, [\n        data,\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setComponentToEditId(editingIden.id);\n    }, [\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setCmpData(components.data.find(function(comp) {\n            return comp.uid === (sectionData === null || sectionData === void 0 ? void 0 : sectionData.__component);\n        }));\n    }, [\n        sectionData,\n        components.data\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.left && setChildComponentData([]);\n    }, [\n        expandedSidebar.left\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), !expandedSidebar.left && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().is__hidden)),\n        children: cmpData && sectionData && componentToEditId !== -1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__wrapper),\n            children: [\n                childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && !activeMediaId && (!mediaInfoData || mediaInfoData.name === \"\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__.LayerSidebarLayout, {}, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 7\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return setEditingIden({\n                                    key: \"\",\n                                    id: -1\n                                });\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"chevron-left\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: (cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.displayName) || cmpData.uid\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: filteredComponents(cmpData.schema.attributes).map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], value = _param[1];\n                        var val = value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, val), {\n                            name: key,\n                            size: 12,\n                            layerPos: \"left\",\n                            value: sectionData[key],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, prevData);\n                                    var field = props.field, value = props.value;\n                                    var curCmpIndex = newData.data.components.findIndex(function(data) {\n                                        return data.__component === editingIden.key && data.id === editingIden.id;\n                                    });\n                                    if (curCmpIndex === -1) {\n                                        return newData;\n                                    }\n                                    newData.data.components[curCmpIndex] = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, newData.data.components[curCmpIndex]), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__._)({}, field.trim(), value));\n                                    // console.log(`[FieldEditor] New data after update:`, newData)\n                                    return newData;\n                                });\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_13__.ComponentList, {}, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 106,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, _this);\n};\n_s(LeftSidebarLayout, \"JQC0MW6nnBgM18/suWAQ8+tf9r8=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LeftSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LeftSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx\n"));

/***/ })

});